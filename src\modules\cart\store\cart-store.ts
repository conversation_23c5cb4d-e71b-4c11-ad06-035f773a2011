import {persist} from 'zustand/middleware';
import {create} from 'zustand';
import {ProductItemType} from '../types/products';
import {useEffect, useState} from 'react';
import {updateProductQuantity} from '../utils/product-quantity-management';
import useCartVisibility from './cart-visibility-store';
import {extractCartItems} from '../services/cart-items-extraction';
import {addCartItemOnServerSide} from '../services/cart-item-addition';
import {deleteCartItemOnServerSide} from '../services/cart-item-deletion';
import {keepPreviousData, useQuery} from '@tanstack/react-query';
import useUserStore from '../../auth/store/user-store';
import {CustomError} from '../../../lib/custom-error';
import {useWishlistStore} from '../../wishlist/store/wishlist-store';
import {WishedProductItemType} from '../../wishlist/types/products';

type State = {
  cartOnServerSideVersion: number;
  cartItems: ProductItemType[];
};

type Action = {
  addProductItem: (
    p: ProductItemType,
    cartAnimation?: boolean,
    checkAuthentication?: boolean,
  ) => void;
  updateProductItemQuantity: (
    id: string,
    quantity: number,
    checkAuthentication?: boolean,
  ) => void;
  removeProductItem: (id: string, checkAuthentication?: boolean) => void;
  emptyCart: () => void;
  setCart: (cartItems: ProductItemType[]) => void;
  setCartOnServerSideVersion: (version: number) => void;
};

export const useCartStore = create(
  persist<{state: State; actions: Action}>(
    (set) => ({
      state: {
        cartItems: [],
        cartOnServerSideVersion: 0,
      },
      actions: {
        setCartOnServerSideVersion: (version) =>
          set(({state}) => ({
            state: {
              cartOnServerSideVersion: version,
              cartItems: state.cartItems,
            },
          })),
        updateProductItemQuantity: (id, quantity, checkAuthentication = true) =>
          set(({state}) => {
            if (quantity > 0) {
              //quantity always should be over 0
              const cartItems = updateProductQuantity(
                state.cartItems,
                id,
                quantity,
                checkAuthentication,
              );

              //updating cart if we found the item already exist in the cart
              if (cartItems)
                return {
                  state: {
                    cartItems,
                    cartOnServerSideVersion: state.cartOnServerSideVersion,
                  },
                };

              //product doesn't exist in our cart
              return {
                state: {
                  cartItems: state.cartItems,
                  cartOnServerSideVersion: state.cartOnServerSideVersion,
                },
              };
            }

            return {
              state: {
                cartItems: state.cartItems,
                cartOnServerSideVersion: state.cartOnServerSideVersion,
              },
            };
          }),
        addProductItem: (p, cartAnimation = true, checkAuthentication = true) =>
          set(({state}) => {
            const cartItems = updateProductQuantity(
              state.cartItems,
              p.id,
              p.cartQuantity,
              true,
            );

            //product already exist in the cart and we just update its quantity
            if (cartItems) {
              const {actions: wishlistActions, state: wishlistState} =
                useWishlistStore.getState();
              const wishlistItem = wishlistState.wishlistItems.find(
                (item: WishedProductItemType) => item.productItemId === p.id,
              );
              if (wishlistItem) {
                wishlistActions.removeProductItem(
                  wishlistItem.productItemId,
                  true,
                );
              }

              //updating quantity on serverside
              return {
                state: {
                  cartItems,
                  cartOnServerSideVersion: state.cartOnServerSideVersion,
                },
              };
            }

            if (cartAnimation) useCartVisibility.getState().setCartIsOpen(true);

            // Remove item from wishlist when added to cart
            const {actions: wishlistActions, state: wishlistState} =
              useWishlistStore.getState();
            const wishlistItem = wishlistState.wishlistItems.find(
              (item: WishedProductItemType) => item.productItemId === p.id,
            );
            if (wishlistItem) {
              wishlistActions.removeProductItem(
                wishlistItem.productItemId,
                true, // Allow server synchronization
              );
            }

            //product doesn't exist and will be added to the cart
            //checking if use is authenticated to store product on server side also
            const {user} = useUserStore.getState();

            if (checkAuthentication && user && user.isAuthenticated) {
              //creating product item on the server side also

              addCartItemOnServerSide({
                id: p.id,
                quantity: p.cartQuantity,
              })
                .then((_) => {
                  useCartStore
                    .getState()
                    .actions.setCartOnServerSideVersion(
                      state.cartOnServerSideVersion + 1,
                    );
                })
                .catch(() => {
                  useCartStore
                    .getState()
                    .actions.removeProductItem(p.id, false);
                });
            }

            return {
              state: {
                cartItems: [p, ...state.cartItems],
                cartOnServerSideVersion: state.cartOnServerSideVersion,
              },
            };
          }),
        removeProductItem: (productItemId, checkAuthentication = true) =>
          set(({state}) => {
            const {user} = useUserStore.getState();

            if (checkAuthentication && user && user.isAuthenticated) {
              //delete product item on the server side also
              //searching product to get it back if deletion doesn't happen on server side
              const productItemToDelete = state.cartItems.find(
                (productItem) => productItem.id === productItemId,
              );

              if (
                productItemToDelete &&
                productItemToDelete.productItemCartId
              ) {
                deleteCartItemOnServerSide(
                  productItemToDelete.productItemCartId,
                )
                  .then(() => {
                    useCartStore
                      .getState()
                      .actions.setCartOnServerSideVersion(
                        state.cartOnServerSideVersion + 1,
                      );
                  })
                  .catch((error: CustomError) => {
                    // Only restore item if it's not a 404 (item doesn't exist on server)
                    if (error.status !== 404 && productItemToDelete) {
                      useCartStore
                        .getState()
                        .actions.addProductItem(
                          productItemToDelete,
                          false,
                          false,
                        );
                    }
                  });
              }
            }

            return {
              state: {
                cartItems: state.cartItems.filter(
                  (productItem) => productItem.id !== productItemId,
                ),
                cartOnServerSideVersion: state.cartOnServerSideVersion,
              },
            };
          }),
        emptyCart: () =>
          set(({state}) => {
            const {user} = useUserStore.getState();

            if (user && user.isAuthenticated) {
              state.cartItems.forEach((item) => {
                if (item.productItemCartId) {
                  deleteCartItemOnServerSide(item.productItemCartId).catch(
                    () => {},
                  );
                }
              });

              // increment server version to trigger refetch
              return {
                state: {
                  cartItems: [],
                  cartOnServerSideVersion: state.cartOnServerSideVersion + 1,
                },
              };
            }

            return {
              state: {
                cartItems: [],
                cartOnServerSideVersion: state.cartOnServerSideVersion,
              },
            };
          }),
        setCart: (cartItems) =>
          set(({state}) => ({
            state: {
              cartItems,
              cartOnServerSideVersion: state.cartOnServerSideVersion,
            },
          })),
      },
    }),
    {
      name: 'cart',
      partialize: (store) =>
        ({state: store.state} as {state: State; actions: Action}),
    },
  ),
);

export const useCart = () => {
  const [hasHydrated, setHasHydrated] = useState(false);
  const store = useCartStore((store) => store);
  const {user} = useUserStore((store) => store);
  const {data} = useQuery({
    queryKey: ['cartItems', user, store.state.cartOnServerSideVersion],
    queryFn: extractCartItems,
    enabled: user !== null && user.isAuthenticated,
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    setHasHydrated(true);
  }, []);

  //fetching user data if user is authenticated
  useEffect(() => {
    if (user && user.isAuthenticated && data) store.actions.setCart(data);
  }, [user, data]);

  return {
    cartItems: hasHydrated ? store.state.cartItems : [],
    addProductItem: store.actions.addProductItem,
    removeProductItem: store.actions.removeProductItem,
    updateProductItemQuantity: store.actions.updateProductItemQuantity,
    emptyCart: store.actions.emptyCart,
  };
};
