import React from 'react';
import {ProductType} from '../../types/products';
import {components} from '../../../../components';

interface Props {
  product: ProductType;
  version?: number;
  lastItem?: boolean;
}

const AppProductCard: React.FC<Props> = ({
  product,
  version = 2,
  lastItem = false,
}): JSX.Element => {
  return (
    <components.ProductCard
      item={product}
      version={version}
      lastItem={lastItem}
    />
  );
};

export default AppProductCard;
