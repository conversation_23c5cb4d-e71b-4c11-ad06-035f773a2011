import { Minus, Plus } from "lucide-react";
import Image from "next/image";
import { ProductItemType } from "../types/products";
import Text from "@/styles/text-styles";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useCartStore } from "../store/cart-store";
import { formatPrice } from "@/modules/catalog/utils/prices-transformation";
import { useEffect, useState } from "react";
import TrashIcon from "@assets/icons/trash";
import useCurrency from "@/modules/catalog/hooks/use-currency";

interface Props {
  productItem: ProductItemType;
}

export default function ProductContainer({ productItem }: Props) {
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );

  const { updateProductItemQuantity, removeProductItem } = useCartStore(
    (store) => store.actions
  );
  const { currency } = useCurrency();

  const promotionIsAvailable =
    productItem?.prices[0].realPrice !==
    productItem?.prices[0].promotionalPrice;

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  return (
    <div className="w-full flex gap-4 p-4 bg-gray-50 rounded-lg">
      {/* Product Image */}
      <div className="flex-shrink-0">
        <Image
          src={productImage}
          alt={productItem.name}
          onError={() => setProductImage("/not-found/product-image.webp")}
          width={100}
          height={100}
          unoptimized
          className="h-24 w-24 rounded-lg object-cover"
        />
      </div>

      {/* Product Details */}
      <div className="flex-1 flex flex-col justify-between ">
        {/* Product Name */}
        <div className="mb-2">
          <Text textStyle="TS5" className="text-black font-medium line-clamp-2">
            {productItem.name}
          </Text>
        </div>
        {/* Price */}
        <div className="mb-3">
          <div className="flex items-center gap-2">
            <Text textStyle="TS6" className="text-black font-bold">
              {`${formatPrice(
                productItem.prices[0].promotionalPrice
              )} ${currency}`}
            </Text>
            {promotionIsAvailable && (
              <Text textStyle="TS7" className="text-gray line-through">
                {`${formatPrice(productItem.prices[0].realPrice)} ${currency}`}
              </Text>
            )}
          </div>
        </div>
        <div className="flex justify-between items-center">
          {/* Quantity Controls */}
          <div className="flex items-center gap-3">
            <Button
              size="tiny"
              className={cn(
                "w-8 h-8 rounded-full  bg-white text-gray hover:text-gray border border-[#EAEAEA]",
                {
                  "opacity-50": productItem.cartQuantity === 1,
                }
              )}
              onClick={() => {
                const newQuantity = productItem.cartQuantity - 1;
                if (newQuantity > 0) {
                  updateProductItemQuantity(productItem.id, newQuantity);
                }
              }}
            >
              <Minus className="h-4 w-4 " />
            </Button>

            <Text
              textStyle="TS6"
              className="text-black font-medium min-w-[20px] text-center"
            >
              {productItem.cartQuantity}
            </Text>

            <Button
              className="w-8 h-8 rounded-full  bg-white text-gray hover:text-primary border border-[#EAEAEA]"
              onClick={() => {
                const newQuantity = productItem.cartQuantity + 1;
                updateProductItemQuantity(productItem.id, newQuantity);
              }}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          {/* Delete Button */}
          <Button
            size="tiny"
            variant="ghost"
            onClick={() => removeProductItem(productItem.id)}
            className="w-8 h-8  bg-[#F3F6FB]  text-white p-2 rounded-full"
          >
            <TrashIcon />
          </Button>
        </div>
      </div>
    </div>
  );
}
