import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {theme} from '../../constants';
import {text} from '../../text';
import {svg} from '../../assets/svg';
import OrderItem from './OrderItem';
import OrderStatus from './OrderStatus';
import {OrderDataType} from '../../modules/checkout/types/orders';
export interface OrderItemType {
  id: string;
  name: string;
  image: string;
  slug: string;
  quantity: number;
  price: number;
  currency: string;
}

export interface OrderType {
  id: string;
  orderNumber: string;
  date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: OrderItemType[];
  total: number;
  currency: string;
  shippingAddress?: string;
  trackingNumber?: string;
}

interface OrderContainerProps {
  order: OrderDataType;
  onPress?: () => void;
  showDetails?: boolean;
  containerStyle?: object;
}

const OrderContainer: React.FC<OrderContainerProps> = ({
  order,
  onPress,
  showDetails = false,
  containerStyle,
}): JSX.Element => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return '#4CAF50';
      case 'shipped':
        return '#2196F3';
      case 'processing':
        return '#FF9800';
      case 'cancelled':
        return '#F44336';
      default:
        return theme.colors.textColor;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Order Placed';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  };

  const renderOrderHeader = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <View>
          <text.H5 style={{marginBottom: 4}}>Order #{order.code}</text.H5>
          <text.T14 style={{color: theme.colors.textColor}}>
            {order.createdAt}
          </text.T14>
        </View>
        <View style={{alignItems: 'flex-end'}}>
          <Text
            style={{
              ...theme.fonts.DMSans_500Medium,
              fontSize: 14,
              color: getStatusColor(order.status),
              marginBottom: 4,
            }}
          >
            {getStatusText(order.status)}
          </Text>
          <text.H5 style={{color: theme.colors.mainColor}}>
            {order.currency}
            {order.total.toFixed(2)}
          </text.H5>
        </View>
      </View>
    );
  };

  const renderOrderItems = () => {
    if (!showDetails) {
      return (
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <text.T14 style={{color: theme.colors.textColor}}>
            {order.items.length} item{order.items.length > 1 ? 's' : ''}
          </text.T14>
          <svg.RightArrowSvg />
        </View>
      );
    }

    return (
      <View style={{marginTop: 16}}>
        {order.items.map((item, index) => (
          <OrderItem
            key={index}
            item={item}
            lastElement={index === order.items.length - 1}
          />
        ))}
      </View>
    );
  };

  const renderOrderStatus = () => {
    if (!showDetails) return null;

    const statusSteps = [
      {
        title: 'Processing',
        description: 'Your order is being processed',
        status: order.status !== 'Processing',
      },
      {
        title: 'Delivered',
        description: 'Your order has been delivered',
        status: order.status === 'Delivered',
      },
      {
        title: 'Cancelled',
        description: 'Your order has been cancelled',
        status: order.status === 'Cancelled',
      },
      {
        title: 'Returned',
        description: 'Your order has been returned',
        status: order.status === 'Returned',
      },
      {
        title: 'Failed',
        description: 'Your order has been failed',
        status: order.status === 'Failed',
      },
      {
        title: 'AwaitingApproval',
        description: 'Your order has been awaitingApproval',
        status: order.status === 'AwaitingApproval',
      },
    ];

    return (
      <View style={{marginTop: 20}}>
        <text.H5 style={{marginBottom: 16}}>Order Status</text.H5>
        {statusSteps.map((step, index) => (
          <OrderStatus
            key={index}
            title={step.title}
            description={step.description}
            status={step.status}
          />
        ))}
      </View>
    );
  };

  const renderShippingInfo = () => {
    if (!showDetails || !order.address.address1) return null;

    return (
      <View style={{marginTop: 20}}>
        <text.H5 style={{marginBottom: 8}}>Shipping Address</text.H5>
        <text.T14 style={{color: theme.colors.textColor, lineHeight: 20}}>
          {order.address.address1}
        </text.T14>
      </View>
    );
  };

  const content = (
    <View>
      {renderOrderHeader()}
      {renderOrderItems()}
      {renderOrderStatus()}
      {renderShippingInfo()}
    </View>
  );

  if (onPress && !showDetails) {
    return (
      <TouchableOpacity
        style={{
          backgroundColor: theme.colors.white,
          borderRadius: 8,
          padding: 16,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: theme.colors.lightBlue,
          ...containerStyle,
        }}
        onPress={onPress}
        activeOpacity={0.8}
      >
        {content}
      </TouchableOpacity>
    );
  }

  return (
    <View
      style={{
        backgroundColor: theme.colors.white,
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        borderWidth: 1,
        borderColor: theme.colors.lightBlue,
        ...containerStyle,
      }}
    >
      {content}
    </View>
  );
};

export default OrderContainer;
